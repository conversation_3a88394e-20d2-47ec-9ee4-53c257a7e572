import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./features/auth/login/login.component').then(
        (m) => m.LoginComponent
      ),
  },
  {
    path: 'dashboard',
    loadComponent: () =>
      import('./features/dashboard/dashboard.component').then(
        (m) => m.DashboardComponent
      ),
    canActivate: [authGuard],
  },
  {
    path: 'customers',
    loadChildren: () =>
      import('./features/customers/customers.routes').then(
        (m) => m.customersRoutes
      ),
    canActivate: [authGuard],
  },
  {
    path: 'accounts',
    loadChildren: () =>
      import('./features/accounts/accounts.routes').then(
        (m) => m.accountsRoutes
      ),
    canActivate: [authGuard],
  },
  {
    path: 'operations',
    loadChildren: () =>
      import('./features/operations/operations.routes').then(
        (m) => m.operationsRoutes
      ),
    canActivate: [authGuard],
  },
  {
    path: '**',
    redirectTo: '/dashboard',
  },
];
